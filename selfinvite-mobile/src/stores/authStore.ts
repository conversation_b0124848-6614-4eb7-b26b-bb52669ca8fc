import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Session, User } from '@supabase/supabase-js'
import { supabaseAuth } from '../../lib/supabase'
import { TokenManager } from '../utils/tokenManager'

interface AuthState {
  // State
  session: Session | null
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  setSession: (session: Session | null) => void
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  clearAuth: () => void
  saveSession: (session: Session) => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      session: null,
      user: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true until hydration is complete

      // Actions
      setSession: async (session) => {
        set({
          session,
          user: session?.user || null,
          isAuthenticated: !!session,
        })

        // Save session to AsyncStorage if it exists
        if (session) {
          await get().saveSession(session)
        }
      },

      setUser: (user) => {
        set({ user })
      },

      setLoading: (isLoading) => {
        set({ isLoading })
      },

      signOut: async () => {
        set({ isLoading: true })
        try {
          const { error } = await supabaseAuth.auth.signOut()
          if (error) {
            console.error('Sign out error:', error)
          }
          
          // Clear stored tokens
          await TokenManager.clearTokens()
          
          // Clear auth state
          set({
            session: null,
            user: null,
            isAuthenticated: false,
            isLoading: false,
          })
        } catch (error) {
          console.error('Sign out error:', error)
          set({ isLoading: false })
        }
      },

      refreshSession: async () => {
        try {
          const { data: { session }, error } = await supabaseAuth.auth.getSession()
          if (error) {
            console.error('Session refresh error:', error)
            return
          }
          
          if (session) {
            get().setSession(session)
            // Use TokenManager to ensure proper persistence
            await TokenManager.saveSession(session)
          } else {
            get().clearAuth()
          }
        } catch (error) {
          console.error('Session refresh error:', error)
        }
      },

      clearAuth: () => {
        set({
          session: null,
          user: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },

      saveSession: async (session: Session) => {
        try {
          // Use TokenManager for consistent token storage
          await TokenManager.saveSession(session)
        } catch (error) {
          console.error('Error saving session:', error)
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist essential data
      partialize: (state) => ({
        session: state.session,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.isLoading = false;
        }
      },
    }
  )
)

// Helper to get access token
export const getAccessToken = (): string | null => {
  const { session } = useAuthStore.getState()
  return session?.access_token || null
}

// Helper to get user
export const getCurrentUser = (): User | null => {
  const { user } = useAuthStore.getState()
  return user
}

export const getCurrentUserId = (): string | null => {
  const { user } = useAuthStore.getState()
  return user?.id || null
} 