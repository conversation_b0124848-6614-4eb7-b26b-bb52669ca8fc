import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query'
import { eventsApi } from '../api/eventsApi'
import { useAuthStore } from '../stores/authStore'
import type { Event, CreateEventRequest, UpdateEventRequest, EventSearchParams, PaginatedResponse } from '../types'

// Query Keys
export const eventKeys = {
  all: ['events'] as const,
  lists: () => [...eventKeys.all, 'list'] as const,
  list: (filters: EventSearchParams) => [...eventKeys.lists(), filters] as const,
  details: () => [...eventKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventKeys.details(), id] as const,
  myHosted: () => [...eventKeys.all, 'my-hosted'] as const,
  myJoined: () => [...eventKeys.all, 'my-joined'] as const,
  myEvents: () => [...eventKeys.all, 'my-events'] as const,
  byUser: (userId?: string) => [...eventKeys.all, 'by-user', userId] as const,
  nearby: (lat: number, lng: number, radius: number) => [...eventKeys.all, 'nearby', lat, lng, radius] as const,
  participants: (eventId: string) => [...eventKeys.all, 'participants', eventId] as const,
}

// Get all events with search/filter
export const useEvents = (params?: EventSearchParams) => {
  return useQuery({
    queryKey: eventKeys.list(params || {}),
    queryFn: () => eventsApi.getEvents(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes,
  })
}

// Get single event by ID
export const useEvent = (eventId: string) => {
  return useQuery({
    queryKey: eventKeys.detail(eventId),
    queryFn: () => eventsApi.getEvent(eventId),
    enabled: !!eventId,
    staleTime: 5 * 60 * 1000,
  })
}

// Get user's hosted events
export const useMyHostedEvents = () => {
  const { isAuthenticated, isLoading } = useAuthStore()

  return useQuery({
    queryKey: eventKeys.myHosted(),
    queryFn: () => eventsApi.getMyHostedEvents(),
    enabled: !isLoading && isAuthenticated,
  })
}

// Get user's joined events
export const useMyJoinedEvents = () => {
  const { isAuthenticated, isLoading } = useAuthStore()

  return useQuery({
    queryKey: eventKeys.myJoined(),
    queryFn: () => eventsApi.getMyJoinedEvents(),
    enabled: !isLoading && isAuthenticated,
  })
}

// Get all user events (hosted + joined) with flag indicating which are hosted
export const useMyEvents = () => {
  const { isAuthenticated, isLoading } = useAuthStore()
  const hostedQuery = useMyHostedEvents();
  const joinedQuery = useMyJoinedEvents();

  return useQuery({
    queryKey: eventKeys.myEvents(),
    queryFn: async () => {
      const [hosted, joined] = await Promise.all([
        eventsApi.getMyHostedEvents(),
        eventsApi.getMyJoinedEvents()
      ]);

      const hostedWithFlag = hosted.map(event => ({ ...event, isHosted: true }));
      const joinedWithFlag = joined.map(event => ({ ...event, isHosted: false }));

      return [...hostedWithFlag, ...joinedWithFlag];
    },
    enabled: !isLoading && isAuthenticated,
    staleTime: 2 * 60 * 1000, // 2 minutes
    // Combine loading states
    meta: {
      isLoading: hostedQuery.isLoading || joinedQuery.isLoading,
      error: hostedQuery.error || joinedQuery.error,
    }
  });
};

// Get events by user ID
export const useEventsByUser = (userId?: string) => {
  const { isAuthenticated, isLoading } = useAuthStore()

  return useQuery({
    queryKey: eventKeys.byUser(userId),
    queryFn: () => eventsApi.getEventsByUser(userId),
    enabled: !isLoading && isAuthenticated && !!userId,
  })
}

// Get events by location
export const useEventsByLocation = (lat: number, lng: number, radius: number = 10) => {
  return useQuery({
    queryKey: eventKeys.nearby(lat, lng, radius),
    queryFn: () => eventsApi.getEventsByLocation(lat, lng, radius),
    enabled: !!lat && !!lng,
  })
}

// Get event participants
export const useEventParticipants = (eventId: string) => {
  return useQuery({
    queryKey: eventKeys.participants(eventId),
    queryFn: () => eventsApi.getEventParticipants(eventId),
    enabled: !!eventId,
  })
}

// Create new event
export const useCreateEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (eventData: CreateEventRequest) => eventsApi.createEvent(eventData),
    onSuccess: (newEvent) => {
      // Invalidate and refetch events lists
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventKeys.myHosted() })
      
      // Add the new event to the cache
      queryClient.setQueryData(eventKeys.detail(newEvent.id), newEvent)
    },
    onError: (error) => {
      console.error('Failed to create event:', error)
    },
  })
}

// Update event
export const useUpdateEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ eventId, eventData }: { eventId: string; eventData: UpdateEventRequest }) =>
      eventsApi.updateEvent(eventId, eventData),
    onSuccess: (updatedEvent, variables) => {
      // Update the event in cache
      queryClient.setQueryData(eventKeys.detail(variables.eventId), updatedEvent)
      
      // Invalidate lists that might contain this event
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventKeys.myHosted() })
    },
    onError: (error) => {
      console.error('Failed to update event:', error)
    },
  })
}

// Delete event
export const useDeleteEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (eventId: string) => eventsApi.deleteEvent(eventId),
    onSuccess: (_, eventId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: eventKeys.detail(eventId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventKeys.myHosted() })
    },
    onError: (error) => {
      console.error('Failed to delete event:', error)
    },
  })
}

// Join event
export const useJoinEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (eventId: string) => eventsApi.joinEvent(eventId),
    onSuccess: (_, eventId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(eventId) })
      queryClient.invalidateQueries({ queryKey: eventKeys.myJoined() })
      queryClient.invalidateQueries({ queryKey: eventKeys.participants(eventId) })
    },
    onError: (error) => {
      console.error('Failed to join event:', error)
    },
  })
}

// Leave event
export const useLeaveEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (eventId: string) => eventsApi.leaveEvent(eventId),
    onSuccess: (_, eventId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(eventId) })
      queryClient.invalidateQueries({ queryKey: eventKeys.myJoined() })
      queryClient.invalidateQueries({ queryKey: eventKeys.participants(eventId) })
    },
    onError: (error) => {
      console.error('Failed to leave event:', error)
    },
  })
}

// Upload event images
export const useUploadEventImages = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ eventId, images }: { eventId: string; images: FormData }) =>
      eventsApi.uploadEventImages(eventId, images),
    onSuccess: (_, variables) => {
      // Invalidate the event to refetch with new images
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(variables.eventId) })
    },
    onError: (error) => {
      console.error('Failed to upload event images:', error)
    },
  })
}

// Cancel event
export const useCancelEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ eventId, reason }: { eventId: string; reason?: string }) =>
      eventsApi.cancelEvent(eventId, reason),
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(variables.eventId) })
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventKeys.myHosted() })
      queryClient.invalidateQueries({ queryKey: eventKeys.myJoined() })
    },
    onError: (error) => {
      console.error('Failed to cancel event:', error)
    },
  })
}

// Publish draft event
export const usePublishEvent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (eventId: string) => eventsApi.publishEvent(eventId),
    onSuccess: (updatedEvent, eventId) => {
      // Update the event in cache
      queryClient.setQueryData(eventKeys.detail(eventId), updatedEvent)
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventKeys.myHosted() })
    },
    onError: (error) => {
      console.error('Failed to publish event:', error)
    },
  })
} 