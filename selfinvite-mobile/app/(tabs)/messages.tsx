import React from 'react';
import { ScrollView, StyleSheet, View, ActivityIndicator, RefreshControl } from 'react-native';
import { Surface, Text, Avatar, Badge, Button, Searchbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { useConversations, useUnreadCount } from '../../src/hooks/useMessages';
import type { Conversation } from '../../src/types';
import log from '@/common/logger';
import { useRouter } from 'expo-router';
import { useEffect } from 'react';
import supabase from '../../lib/supabase';
import { useAuthStore } from '../../src/stores';

export default function MessagesScreen() {
  const theme = useTheme();
  const router = useRouter();
  const [refreshing, setRefreshing] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();

  // Check if user is logged in
  const currentUserId = user?.id;
  const isLoggedIn = isAuthenticated;

  // Fetch data using React Query only if user is logged in
  const { 
    data: conversations = [], 
    isLoading, 
    error, 
    refetch 
  } = useConversations();
  
  const { data: unreadData } = useUnreadCount();
  const unreadCount = unreadData?.count || 0;

  // Real-time updates for conversations (only if logged in)
  useEffect(() => {
    if (!isLoggedIn) return;
    
    // No specific conversation filter needed for messages screen

    const channel = supabase
      .channel('conversations')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversations',
          // No specific filter needed for messages screen
        },
        (payload) => {
          log.info('Conversation updated:', payload);
          // Refetch conversations to get latest data
          refetch();
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages'
        },
        (payload) => {
          log.info('New message received:', payload);
          // Refetch conversations to update last message and unread count
          refetch();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [refetch, isLoggedIn]);

  // Handle pull-to-refresh
  const onRefresh = React.useCallback(async () => {
    if (!isLoggedIn) return;
    
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      log.error('Error refreshing conversations:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch, isLoggedIn]);

  // Handle conversation selection
  const handleConversationPress = (conversation: Conversation) => {
    router.push(`/chat/${conversation.id}`);
  };

  // Format time ago utility function
  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInWeeks = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 7));

    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInHours < 24) return `${diffInHours}h`;
    if (diffInDays < 7) return `${diffInDays}d`;
    if (diffInWeeks < 4) return `${diffInWeeks}w`;
    return `${Math.floor(diffInDays / 30)}mo`;
  };

  // Show loading while auth state is being restored
  if (authLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show login prompt if user is not authenticated
  if (!isLoggedIn) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loginContainer}>
          <Text style={[styles.loginTitle, { color: theme.colors.onSurface }]}>
            You need to login
          </Text>
          <Text style={[styles.loginSubtitle, { color: theme.colors.onSurface }]}>
            Please sign in to view your messages
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading conversations...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.errorScrollContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              Failed to load conversations. Please check your connection and try again.
            </Text>
            <Button 
              mode="contained" 
              onPress={onRefresh}
              style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
              loading={refreshing}
              disabled={refreshing}
            >
              {refreshing ? 'Retrying...' : 'Retry'}
            </Button>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Header */}
        <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.title, { color: theme.colors.onSurface }]}>
            Messages
            {unreadCount > 0 && (
              <Badge style={{ marginLeft: 8, backgroundColor: theme.colors.primary }}>
                {unreadCount}
              </Badge>
            )}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.onSurface }]}>
            Chat with hosts and guests
          </Text>
        </Surface>

        {/* Search Bar */}
        <Surface style={[styles.searchSection, { backgroundColor: theme.colors.surface }]}>
          <Searchbar
            placeholder="Search conversations..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            iconColor={theme.colors.onSurface}
          />
        </Surface>

        {/* Conversations List */}
        <Surface style={[styles.conversationsSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Conversations ({conversations.length})
          </Text>
          
          {(() => {
            const filteredConversations = searchQuery.trim() 
              ? conversations.filter(conversation => {
                  const query = searchQuery.toLowerCase();
                  // Search in participant names
                  const participantMatch = conversation.participants.some(participant =>
                    participant.nickname.toLowerCase().includes(query)
                  );
                  
                  // Search in last message content
                  const messageMatch = conversation.lastMessage?.body.toLowerCase().includes(query);
                  
                  return participantMatch || messageMatch;
                })
              : conversations;

              log.debug('Filtered conversations:', filteredConversations);
              log.debug('Current user ID:', currentUserId);
            
            return filteredConversations.length === 0 ? (
              <Surface style={[styles.emptyState, { backgroundColor: theme.colors.surface }]}>
                <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>
                  {searchQuery.trim() ? 'No conversations found' : 'No conversations yet'}
                </Text>
                <Text style={[styles.emptySubtitle, { color: theme.colors.onSurface }]}>
                  {searchQuery.trim() 
                    ? 'Try adjusting your search terms' 
                    : 'Start participating in events to connect with other food lovers!'
                  }
                </Text>
              </Surface>
            ) : (
              filteredConversations.map((conversation, index) => {
                // Get the other participant (not the current user)
                const otherParticipant = conversation.participants.find(p => p.id !== currentUserId) || conversation.participants[0];
                
                // Skip if no participant found
                if (!otherParticipant) return null;
                
                const initials = otherParticipant.nickname.split(' ').map(n => n[0]).join('').toUpperCase();
                
                // Format time
                const timeAgo = formatTimeAgo(conversation.updatedAt);
                
                return (
                  <React.Fragment key={conversation.id}>
                    <Surface 
                      style={[styles.conversationItem, { backgroundColor: theme.colors.surface }]}
                      onTouchEnd={() => handleConversationPress(conversation)}
                    >
                      <View style={styles.conversationLeft}>
                        <Avatar.Text 
                          size={50} 
                          label={initials} 
                          style={{ backgroundColor: theme.colors.primary }} 
                        />
                        <View style={styles.conversationInfo}>
                          <Text style={[styles.conversationName, { color: theme.colors.onSurface }]}>
                            {otherParticipant.nickname}
                          </Text>
                                                  <Text style={[styles.conversationPreview, { color: theme.colors.onSurface }]}>
                          {conversation.lastMessage?.body || 'No messages yet'}
                        </Text>
                        </View>
                      </View>
                      <View style={styles.conversationRight}>
                        <Text style={[styles.conversationTime, { color: theme.colors.onSurface }]}>
                          {timeAgo}
                        </Text>
                        {conversation.unreadCount > 0 && (
                          <Badge style={{ backgroundColor: theme.colors.primary }}>
                            {conversation.unreadCount}
                          </Badge>
                        )}
                      </View>
                    </Surface>
                    {index < filteredConversations.length - 1 && (
                      <View style={[styles.divider, { backgroundColor: '#E0E0E0' }]} />
                    )}
                  </React.Fragment>
                );
              }).filter(Boolean)
            );
          })()}
        </Surface>

        {/* Empty State (when no messages) */}
        {/* 
        <Surface style={[styles.emptyState, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>No messages yet</Text>
          <Text style={[styles.emptySubtitle, { color: theme.colors.onSurface }]}>
            Start participating in events to connect with other food lovers!
          </Text>
        </Surface>
        */}

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  conversationsSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  searchSection: {
    padding: 16,
    marginBottom: 8,
  },
  searchBar: {
    elevation: 0,
    backgroundColor: 'transparent',
  },
  conversationItem: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  conversationLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  conversationInfo: {
    marginLeft: 12,
    flex: 1,
  },
  conversationName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  conversationPreview: {
    fontSize: 14,
    opacity: 0.7,
  },
  conversationRight: {
    alignItems: 'flex-end',
  },
  conversationTime: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 4,
  },
  divider: {
    height: 1,
    marginLeft: 78,
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  loginSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorScrollContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    borderRadius: 8,
    marginTop: 8,
  },
}); 