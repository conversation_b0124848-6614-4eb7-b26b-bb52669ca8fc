import 'dotenv/config';

const IS_DEV = process.env.APP_VARIANT === 'development';
const IS_PREVIEW = process.env.APP_VARIANT === 'preview';

export default {
  expo: {
    name: IS_DEV ? 'Selfinvite (Dev)' : IS_PREVIEW ? 'Selfinvite (Preview)' : 'Selfinvite',
    slug: 'selfinvite-mobile',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'automatic',
    scheme: 'selfinvite',

    assetBundlePatterns: [
      '**/*'
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: IS_DEV || IS_PREVIEW ? 'com.selfinvite.mobile.preview' : 'com.selfinvite.mobile',
      infoPlist: {
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: true,
          NSExceptionDomains: {
            'api.selfinvite.eu': {
              NSExceptionAllowsInsecureHTTPLoads: true,
              NSExceptionMinimumTLSVersion: '1.0',
              NSIncludesSubdomains: true
            },
            localhost: {
              NSExceptionAllowsInsecureHTTPLoads: true
            }
          }
        },
        ITSAppUsesNonExemptEncryption: false
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#F24958'
      },
      package: IS_DEV || IS_PREVIEW ? 'com.selfinvite.mobile.preview' : 'com.selfinvite.mobile',
      permissions: [
        'android.permission.ACCESS_COARSE_LOCATION',
        'android.permission.ACCESS_FINE_LOCATION'
      ],
      edgeToEdgeEnabled: true,
      config: {
        googleMaps: {
          apiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || 'dev-google-maps-key'
        }
      },
      intentFilters: [
        {
          "action": "VIEW",
          "autoVerify": true,
          "data": [
            {
              "scheme": "selfinvite"
            }
          ],
          "category": ["BROWSABLE", "DEFAULT"]
        }
      ]
    },
    web: {
      favicon: './assets/favicon.png'
    },
    plugins: [
      'expo-router',
      [
        'expo-splash-screen',
        {
          backgroundColor: '#Dd4e58',
          image: './assets/icon.png',
          resizeMode: 'contain',
          dark: {
            image: './assets/icon.png',
            backgroundColor: '#000000',
            resizeMode: 'contain'
          }
        }
      ],
      'expo-font',
      'expo-secure-store',
      'expo-location',
      [
        'expo-notifications',
        {
          icon: './assets/notification-icon.png',
          color: '#F24958',
          androidMode: 'default',
          androidCollapsedTitle: 'Selfinvite',
          iosDisplayInForeground: true
        }
      ],
      'expo-camera',
      'expo-media-library',
      'expo-image-picker',
      'expo-web-browser',
      'expo-localization',
      [
        '@rnmapbox/maps',
        {
          RNMapboxMapsAccessToken:
            process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN || 'dev-mapbox-token'
        }
      ]
    ],
    experiments: {
      typedRoutes: true,
      reactCanary: true
    },
    extra: {
      router: {
        origin: false
      },
      eas: {
        projectId: '00d2df2e-0cb4-457f-b775-da1661161e4b'
      },
      // Environment-specific configurations
      environments: {
        development: {
          supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://dev.supabase.co',
          supabaseKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'dev-key',
          endpointUrl: process.env.EXPO_PUBLIC_ENDPOINT_URL || 'https://dev.selfinvite.eu',
          supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'dev-key',
          algoliaAppId: process.env.EXPO_PUBLIC_ALGOLIA_APP_ID || 'DEV_ALGOLIA_APP_ID',
          algoliaApiKey: process.env.EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY || 'dev-algolia-key',
          stripePublishableKey: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_dev',
          mapboxAccessToken: process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN || 'dev-mapbox-token',
          googleMapsApiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || 'dev-google-maps-key',
          googleClientId: process.env.GOOGLE_CLIENT_ID || 'dev-google-client-id',
          googleClientSecret: process.env.GOOGLE_CLIENT_SECRET || 'dev-google-client-secret',
        },
        preview: {
          supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://preview.supabase.co',
          supabaseKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'preview-key',
          supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'preview-key',
          endpointUrl: process.env.EXPO_PUBLIC_ENDPOINT_URL || 'https://preview.selfinvite.eu',
          algoliaAppId: process.env.EXPO_PUBLIC_ALGOLIA_APP_ID || 'PREVIEW_ALGOLIA_APP_ID',
          algoliaApiKey: process.env.EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY || 'preview-algolia-key',
          stripePublishableKey: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_preview',
          mapboxAccessToken: process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN || 'preview-mapbox-token',
          googleMapsApiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || 'preview-google-maps-key',
          googleClientId: process.env.GOOGLE_CLIENT_ID || 'preview-google-client-id',
          googleClientSecret: process.env.GOOGLE_CLIENT_SECRET || 'preview-google-client-secret',
        },
        production: {
          supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://prod.supabase.co',
          endpointUrl: process.env.EXPO_PUBLIC_ENDPOINT_URL || 'https://prod.selfinvite.eu',
          supabaseKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'prod-key',
          supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'prod-key',
          algoliaAppId: process.env.EXPO_PUBLIC_ALGOLIA_APP_ID || 'PROD_ALGOLIA_APP_ID',
          algoliaApiKey: process.env.EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY || 'prod-algolia-key',
          stripePublishableKey: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_live_prod',
          mapboxAccessToken: process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN || 'prod-mapbox-token',
          googleMapsApiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || 'prod-google-maps-key',
          googleClientId: process.env.GOOGLE_CLIENT_ID || 'prod-google-client-id',
          googleClientSecret: process.env.GOOGLE_CLIENT_SECRET || 'prod-google-client-secret',
        }
      },
      // Current environment
      appEnv: process.env.EXPO_PUBLIC_APP_ENV || 'preview',
    }
  }
};
