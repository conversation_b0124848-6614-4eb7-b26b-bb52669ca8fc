import React from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Avatar, Surface, Text, useTheme } from 'react-native-paper';

interface EventCardProps {
  title: string;
  date: string;
  location: string;
  status: string;
  statusColor?: string;
  media?: string | null;
  userAvatar?: string | null;
  onPress?: () => void;
}

export default function EventCard({ 
  title, 
  date, 
  location, 
  status, 
  statusColor,
  media,
  userAvatar,
  onPress
}: EventCardProps) {
  const theme = useTheme();

  const CardContent = () => (
    <>
      {media && <Image source={{ uri: media }} style={styles.eventMedia} />}
      <View style={styles.contentContainer}>
        <View style={styles.headerContainer}>
          {userAvatar && <Avatar.Image size={40} source={{ uri: userAvatar }} style={styles.userAvatar} />}
          <View style={styles.titleContainer}>
            <Text style={[styles.eventTitle, { color: theme.colors.onSurface }]}>{title}</Text>
          </View>
        </View>
        <Text style={[styles.eventDate, { color: theme.colors.primary }]}>{date}</Text>
        <Text style={[styles.eventLocation, { color: theme.colors.onSurface }]}>{location}</Text>
        <Text style={[styles.eventStatus, { color: statusColor || theme.colors.primary }]}>{status}</Text>
      </View>
    </>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        <Surface style={[styles.eventCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <CardContent />
        </Surface>
      </TouchableOpacity>
    );
  }

  return (
    <Surface style={[styles.eventCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <CardContent />
    </Surface>
  );
}

const styles = StyleSheet.create({
  eventCard: {
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    overflow: 'hidden',
  },
  eventMedia: {
    height: 150,
    width: '100%',
  },
  contentContainer: {
    padding: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  userAvatar: {
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  eventDate: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  eventLocation: {
    fontSize: 14,
    marginBottom: 4,
    opacity: 0.7,
  },
  eventStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
}); 